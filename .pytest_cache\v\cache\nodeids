["test_qingbaotong_pytest.py::TestDifyIntegration::test_invoke_function_structure", "test_qingbaotong_pytest.py::TestDifyIntegration::test_main_function_structure", "test_qingbaotong_pytest.py::TestExtensibility::test_add_new_tool_example", "test_qingbaotong_pytest.py::TestExtensibility::test_tool_composition_extensibility", "test_qingbaotong_pytest.py::TestOriginalTestCases::test_original_case_1", "test_qingbaotong_pytest.py::TestOriginalTestCases::test_original_case_2", "test_qingbaotong_pytest.py::TestOriginalTestCases::test_original_case_3", "test_qingbaotong_pytest.py::TestOriginalTestCases::test_original_case_4", "test_qingbaotong_pytest.py::TestOriginalTestCases::test_original_case_5", "test_qingbaotong_pytest.py::TestOriginalTestCases::test_original_case_6", "test_qingbaotong_pytest.py::TestQingBaoTongSystem::test_brand_normalization", "test_qingbaotong_pytest.py::TestQingBaoTongSystem::test_industry_normalization", "test_qingbaotong_pytest.py::TestQingBaoTongSystem::test_platform_normalization", "test_qingbaotong_pytest.py::TestQueryIntentParsing::test_parse_comparison_query", "test_qingbaotong_pytest.py::TestQueryIntentParsing::test_parse_growth_rate_query", "test_qingbaotong_pytest.py::TestQueryIntentParsing::test_parse_ranking_query", "test_qingbaotong_pytest.py::TestQueryIntentParsing::test_parse_single_brand_query", "test_qingbaotong_pytest.py::TestSQLGeneration::test_brand_growth_rate_sql", "test_qingbaotong_pytest.py::TestSQLGeneration::test_brand_market_share_sql", "test_qingbaotong_pytest.py::TestSQLGeneration::test_brand_ranking_sql", "test_qingbaotong_pytest.py::TestSQLGeneration::test_brand_sales_amount_sql", "test_qingbaotong_pytest.py::TestSQLGeneration::test_brand_sales_volume_sql", "test_qingbaotong_pytest.py::TestSQLGeneration::test_multi_platform_sales_sql", "test_qingbaotong_pytest.py::TestSmartQuery::test_smart_query_structure", "test_qingbaotong_pytest.py::TestToolComposition::test_analyze_brand_performance_structure", "test_qingbaotong_pytest.py::TestToolComposition::test_compare_brands_structure"]
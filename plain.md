# 你是一名AI开发专家，精通python，精通dify，精通agent
- 接下来写的代码是dify里面的python脚本，所以有很多限制，只能在dify python脚本允许范围内：例如不能用langchain
- python工具或者函数必须是python 3 sdk 内置的，语法尽可能简洁，别花哨用奇淫技巧
# 任务
- 阅读dev.md，读懂开发文档
- 阅读test_sql.py：是基于dev.md写的一个测试
- 我想把test_sql.py里面的问题对做成function，也就是用python function封装sql，入参做成动态的，然后在function里面动态生成sql：例如
  ```md
  2024年{品牌}在全平台的销售额是多少？
  SELECT SUM(`品牌销额`) as 销售额 FROM 情报通 WHERE `品牌名称` = '{品牌}' AND `平台` = '全平台' AND `销售年月` like '2025-%'
  ```
- 虽然不能用Langgraph，我想在脚本实现类似langraph动态组合并调用tool use的效果（可能得结合反射类似），例如比对李宁跟安踏在全平台的销量
- tool use要方便扩展

## dify 代码执行模板，入参可以变化，但返回值必须放result，必须是string
def main(intent_result: str) -> dict:
      result = invoke(intent_result)
      return {
            "result": "必须是string"
        }

def invoke(intent_result: str) -> dict:
    # do something       
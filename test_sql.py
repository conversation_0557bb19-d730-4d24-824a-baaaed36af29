#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import base64
import json
import time
from typing import Dict, List, Tuple

class QingBaoTongAPITester:
    def __init__(self, api_url: str = "http://10.232.153.230:8088/filter_data_md"):
        self.api_url = api_url
        self.headers = {
            "Content-Type": "application/json"
        }
        
    def encode_sql(self, sql: str) -> str:
        """将SQL语句进行Base64编码"""
        return base64.b64encode(sql.encode('utf-8')).decode('utf-8')
    
    def call_api(self, sql: str) -> Dict:
        """调用API接口"""
        encoded_sql = self.encode_sql(sql)
        payload = {
            "result_key": encoded_sql
        }
        
        try:
            response = requests.post(
                self.api_url, 
                headers=self.headers, 
                json=payload,
                timeout=30
            )
            
            return {
                "status_code": response.status_code,
                "response": response.json() if response.status_code == 200 else response.text,
                "success": response.status_code == 200
            }
        except requests.exceptions.RequestException as e:
            return {
                "status_code": 0,
                "response": str(e),
                "success": False
            }
    
    def run_test(self, test_cases: Dict[str, Tuple[str, str]]) -> None:
        """运行测试用例"""
        print("=" * 80)
        print("情报通 API 测试开始")
        print("=" * 80)
        
        results = []
        
        for i, (test_name, (question, sql)) in enumerate(test_cases.items(), 1):
            print(f"\n【测试 {i}】{test_name}")
            print(f"问题: {question}")
            print(f"SQL: {sql}")
            print("-" * 60)
            
            start_time = time.time()
            result = self.call_api(sql)
            end_time = time.time()
            
            print(f"状态码: {result['status_code']}")
            print(f"响应时间: {end_time - start_time:.2f}s")
            print(f"请求成功: {'✅' if result['success'] else '❌'}")
            
            if result['success']:
                print("响应数据:")
                print(json.dumps(result['response'], ensure_ascii=False, indent=2))
            else:
                print(f"错误信息: {result['response']}")
            
            results.append({
                "test_name": test_name,
                "question": question,
                "sql": sql,
                "success": result['success'],
                "status_code": result['status_code'],
                "response_time": end_time - start_time,
                "response": result['response']
            })
            
            print("=" * 80)
            
            # 避免请求过于频繁
            if i < len(test_cases):
                time.sleep(1)
        
        # 输出测试总结
        self.print_summary(results)
    
    def print_summary(self, results: List[Dict]) -> None:
        """打印测试总结"""
        print("\n" + "=" * 80)
        print("测试总结")
        print("=" * 80)
        
        success_count = sum(1 for r in results if r['success'])
        total_count = len(results)
        avg_response_time = sum(r['response_time'] for r in results) / total_count
        
        print(f"总测试数: {total_count}")
        print(f"成功数: {success_count}")
        print(f"失败数: {total_count - success_count}")
        print(f"成功率: {success_count/total_count*100:.1f}%")
        print(f"平均响应时间: {avg_response_time:.2f}s")
        
        print("\n详细结果:")
        for i, result in enumerate(results, 1):
            status = "✅" if result['success'] else "❌"
            print(f"{i}. {result['test_name']} - {status} ({result['response_time']:.2f}s)")


def main():
    # 测试用例定义
    test_cases = {
        "品牌销售额查询": (
            "2025年4月安踏在全平台的销售额是多少？",
            "SELECT SUM(`品牌销额`) as 销售额 FROM 情报通 WHERE `品牌名称` = '安踏' AND `平台` = '全平台' AND `销售年月` = '2025-04-01'"
        ),
        "品牌销售额查询": (
            "2024年安踏在全平台的销售额是多少？",
            "SELECT SUM(`品牌销额`) as 销售额 FROM 情报通 WHERE `品牌名称` = '安踏' AND `平台` = '全平台' AND `销售年月` like '2025-%'"
        ),
        
        "品牌增速计算": (
            "2025年3月李宁在京东平台的销量增速是多少？",
            "SELECT `品牌销量`, `同期品牌销量`, (`品牌销量` - `同期品牌销量`) / `同期品牌销量` * 100 AS '增速' FROM 情报通 WHERE `销售年月` = '2025-03-01' AND `品牌名称` = '李宁' AND `平台` = '京东'"
        ),
        "排名查询": (
            "2025年2月天猫平台运动鞋服行业销售额排名前5的品牌有哪些？",
            "SELECT `品牌名称`, `品牌销额`, `本期排名` FROM 情报通 WHERE `平台` = '天猫' AND `行业` = '运动鞋服' AND `销售年月` = '2025-02-01' ORDER BY `本期排名` ASC LIMIT 5"
        ),
        "多平台汇总查询": (
            "2024年12月耐克在天猫、京东、抖音三个平台的总销售额是多少？",
             "SELECT SUM(`品牌销额`) AS '品牌销额总和' FROM 情报通 WHERE `销售年月` = '2024-12-01' AND `品牌名称` = '耐克' AND `平台` IN ('天猫', '京东', '抖音')"
        ),
        "销量与市场份额查询": (
          "2025年3月斐乐在全平台的销量和市场份额是多少？",
          "SELECT SUM(`品牌销量`) AS '品牌销量', SUM(`本期行业份额`) AS '本期行业份额' FROM 情报通 WHERE `销售年月` = '2025-03-01' AND `平台` = '全平台' AND `品牌名称` = '斐乐'"
        )
    }
    
    # 创建测试器实例
    tester = QingBaoTongAPITester()
    
    # 运行测试
    tester.run_test(test_cases)


if __name__ == "__main__":
    main()

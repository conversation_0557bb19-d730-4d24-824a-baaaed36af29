#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
OpenRouter集成演示
展示如何使用OpenRouter API进行结构化意图提取，然后调用情报通SQL查询系统
"""

import requests
import json
from qingbaotong_system import QingBaoTongSystem, main

# OpenRouter配置
OPENROUTER_API_URL = "https://openrouter.ai/api/v1/chat/completions"
OPENROUTER_API_KEY = "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"

class OpenRouterIntegration:
    """OpenRouter与情报通系统集成"""
    
    def __init__(self):
        self.openrouter_headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {OPENROUTER_API_KEY}"
        }
        self.qbt_system = QingBaoTongSystem()
    
    def extract_intent_with_openrouter(self, user_query: str) -> dict:
        """使用OpenRouter API提取结构化意图"""
        
        # 构造提示词
        system_prompt = """你是一个电商数据查询意图解析专家。请根据用户的自然语言查询，提取出结构化的查询意图。

数据表结构：
- 表名：情报通
- 维度字段：品牌名称、平台、销售年月、行业、日期类型
- 指标字段：品牌销额、品牌销量、本期排名、本期行业份额等

支持的查询类型：
- sales_amount: 销售额查询
- sales_volume: 销量查询  
- growth_rate: 增速查询
- ranking: 排名查询
- comparison: 品牌比较
- multi_platform: 多平台汇总
- market_share: 市场份额查询
- brand_analysis: 品牌综合分析

品牌映射：
- FILA/flia -> 斐乐
- Nike -> 耐克
- Adidas -> 阿迪
- lining -> 李宁
- 安踏 -> 安踏

平台映射：
- 所有平台/全部平台/整体平台 -> 全平台
- JD -> 京东
- 淘宝/Tmall -> 天猫
- douyin -> 抖音

行业映射：
- 母婴/童装/婴幼儿用品 -> 母婴鞋服
- 户外 -> 户外鞋服
- 运动/体育用品 -> 运动鞋服

时间格式：
- 年月格式：YYYY-MM-01 (如2025-04-01)
- 年度格式：YYYY (如2024)

请严格按照以下JSON格式返回，不要添加任何其他文字：
{
  "brands": ["品牌1", "品牌2"],
  "platforms": ["平台1"],
  "metrics": ["指标1"],
  "time_period": "时间",
  "query_type": "查询类型",
  "industry": "行业",
  "limit": 数字
}"""

        payload = {
            "model": "google/gemini-2.5-flash-preview-05-20",
            "messages": [
                {
                    "role": "system",
                    "content": system_prompt
                },
                {
                    "role": "user", 
                    "content": f"请解析以下查询：{user_query}"
                }
            ],
            "temperature": 0.1,
            "max_tokens": 500
        }
        
        try:
            response = requests.post(
                OPENROUTER_API_URL,
                headers=self.openrouter_headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                # 尝试解析JSON
                try:
                    intent = json.loads(content)
                    return {
                        "success": True,
                        "intent": intent,
                        "raw_response": content
                    }
                except json.JSONDecodeError:
                    # 如果不是纯JSON，尝试提取JSON部分
                    import re
                    json_match = re.search(r'\{.*\}', content, re.DOTALL)
                    if json_match:
                        try:
                            intent = json.loads(json_match.group())
                            return {
                                "success": True,
                                "intent": intent,
                                "raw_response": content
                            }
                        except:
                            pass
                    
                    return {
                        "success": False,
                        "error": "无法解析返回的JSON格式",
                        "raw_response": content
                    }
            else:
                return {
                    "success": False,
                    "error": f"OpenRouter API调用失败: {response.status_code}",
                    "response": response.text
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"OpenRouter API调用异常: {str(e)}"
            }
    
    def process_query_with_llm(self, user_query: str) -> dict:
        """完整的查询处理流程：意图提取 -> SQL生成 -> 执行查询"""
        
        print(f"🔍 原始查询: {user_query}")
        print("-" * 60)
        
        # 步骤1: 使用OpenRouter提取意图
        print("📝 步骤1: 使用OpenRouter提取结构化意图...")
        intent_result = self.extract_intent_with_openrouter(user_query)
        
        if not intent_result["success"]:
            return {
                "success": False,
                "error": "意图提取失败",
                "details": intent_result
            }
        
        structured_intent = intent_result["intent"]
        print(f"✅ 提取的意图: {json.dumps(structured_intent, ensure_ascii=False, indent=2)}")
        print()
        
        # 步骤2: 使用情报通系统处理结构化查询
        print("🛠️  步骤2: 生成SQL并执行查询...")
        query_result = self.qbt_system.process_structured_query(structured_intent)
        
        print(f"✅ 查询结果: {json.dumps(query_result, ensure_ascii=False, indent=2)}")
        print()
        
        return {
            "success": True,
            "original_query": user_query,
            "extracted_intent": structured_intent,
            "query_result": query_result,
            "llm_raw_response": intent_result.get("raw_response")
        }
    
    def demo_various_queries(self):
        """演示各种类型的查询"""
        
        test_queries = [
            "2025年4月安踏在全平台的销售额是多少？",
            "2024年李宁在全平台的销售额是多少？",
            "2025年3月李宁在京东平台的销量增速是多少？",
            "比较李宁和安踏在全平台的销量",
            "2025年2月天猫平台运动鞋服行业销售额排名前5的品牌有哪些？",
            "2024年12月耐克在天猫、京东、抖音三个平台的总销售额是多少？",
            "2025年3月斐乐在全平台的销量和市场份额是多少？"
        ]
        
        print("🚀 OpenRouter + 情报通系统集成演示")
        print("=" * 80)
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n📋 测试 {i}/{len(test_queries)}")
            print("=" * 80)
            
            result = self.process_query_with_llm(query)
            
            if result["success"]:
                print("🎉 处理成功!")
            else:
                print(f"❌ 处理失败: {result['error']}")
            
            print("=" * 80)
            
            # 添加延迟避免API限制
            import time
            time.sleep(2)

def demo_direct_tool_calls():
    """演示直接工具调用"""
    print("\n🔧 直接工具调用演示")
    print("=" * 80)
    
    system = QingBaoTongSystem()
    
    # 直接调用工具函数
    tool_demos = [
        {
            "name": "品牌销售额查询",
            "tool": "get_brand_sales_amount",
            "params": {"brand": "安踏", "platform": "全平台", "year_month": "2025-04-01"}
        },
        {
            "name": "品牌增速查询", 
            "tool": "get_brand_growth_rate",
            "params": {"brand": "李宁", "platform": "京东", "year_month": "2025-03-01"}
        },
        {
            "name": "品牌排名查询",
            "tool": "get_brand_ranking", 
            "params": {"platform": "天猫", "industry": "运动鞋服", "year_month": "2025-02-01", "limit": 5}
        },
        {
            "name": "品牌比较",
            "tool": "compare_brands",
            "params": {"brands": ["李宁", "安踏"], "platform": "全平台", "metric": "销量"}
        }
    ]
    
    for demo in tool_demos:
        print(f"\n🔧 {demo['name']}")
        print(f"工具: {demo['tool']}")
        print(f"参数: {json.dumps(demo['params'], ensure_ascii=False)}")
        print("-" * 40)
        
        result = system.execute_tool_function(demo['tool'], **demo['params'])
        print(f"结果: {json.dumps(result, ensure_ascii=False, indent=2)}")
        print()

def demo_dify_integration():
    """演示Dify集成"""
    print("\n📱 Dify集成演示")
    print("=" * 80)
    
    # 模拟Dify传入的结构化意图
    dify_inputs = [
        {
            "name": "结构化意图输入",
            "input": json.dumps({
                "brands": ["安踏"],
                "platforms": ["全平台"],
                "query_type": "sales_amount",
                "time_period": "2025-04-01"
            }, ensure_ascii=False)
        },
        {
            "name": "简单文本输入",
            "input": "查询销售额"
        }
    ]
    
    for demo in dify_inputs:
        print(f"\n📱 {demo['name']}")
        print(f"输入: {demo['input']}")
        print("-" * 40)
        
        result = main(demo['input'])
        print(f"返回: {result}")
        
        # 解析返回的JSON
        try:
            parsed_result = json.loads(result['result'])
            print(f"解析结果: {json.dumps(parsed_result, ensure_ascii=False, indent=2)}")
        except:
            print("无法解析返回的JSON")
        print()

def main_demo():
    """主演示函数"""
    try:
        # 创建集成实例
        integration = OpenRouterIntegration()
        
        # 运行各种演示
        print("OpenRouter + 情报通SQL查询系统 完整演示")
        print("基于大模型结构化意图提取的动态SQL生成系统")
        print("=" * 80)
        
        # 演示1: OpenRouter集成查询
        print("\n🤖 演示1: OpenRouter智能意图提取 + SQL生成")
        integration.demo_various_queries()
        
        # 演示2: 直接工具调用
        demo_direct_tool_calls()
        
        # 演示3: Dify集成
        demo_dify_integration()
        
        print("\n" + "=" * 80)
        print("🎉 演示完成!")
        print("=" * 80)
        print("\n✅ 系统特点:")
        print("  🤖 集成OpenRouter大模型进行智能意图提取")
        print("  🛠️  提供丰富的SQL生成工具函数")
        print("  🔄 支持动态工具组合调用")
        print("  📱 完全兼容Dify平台")
        print("  🎯 精确的品牌/平台/时间标准化")
        print("  📊 支持多种查询类型")
        print("  🚀 易于扩展新功能")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main_demo()
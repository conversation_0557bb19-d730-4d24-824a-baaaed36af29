 
# 情报通SQL查询系统开发文档

## 1. 系统概述
情报通SQL查询系统是一个基于自然语言的电商数据分析工具，支持用户通过自然语言查询各品牌在不同平台的销售数据，系统会自动生成SQL语句并执行查询。

## 2. 数据表结构

### 2.1 表名
`情报通`

### 2.2 字段定义

#### 维度字段 (TEXT类型)
| 字段名 | 类型 | 说明 | 约束 |
|--------|------|------|------|
| 日期类型 | TEXT | 数据类型标识 | - |
| 行业 | TEXT | 行业分类 | 取值范围：母婴鞋服、运动鞋服、户外鞋服 |
| 销售年月 | TEXT | 销售时间 | 格式：YYYY-MM-01 (如2025-02-01) |
| 平台 | TEXT | 销售平台 | 取值范围：抖音、京东、天猫、全平台 |
| 品牌名称 | TEXT | 品牌名称 | 标准中文名称 |

#### 指标字段 (REAL类型)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| 品牌销量 | REAL | 品牌销售数量 |
| 同期品牌销量 | REAL | 去年同期的品牌销售数量 |
| 品牌销额 | REAL | 品牌销售金额 |
| 同期品牌销额 | REAL | 去年同期的品牌销售金额 |
| 行业销量 | REAL | 整个行业销售数量 |
| 同期行业销量 | REAL | 去年同期行业销售数量 |
| 行业销额 | REAL | 整个行业销售金额 |
| 同期行业销额 | REAL | 去年同期行业销售金额 |
| 本期排名 | REAL | 当前期间排名 |
| 同期排名 | REAL | 去年同期排名 |
| 排名变化 | REAL | 本期排名-同期排名 |
| 本期行业份额 | REAL | 当前市场份额百分比 |
| 同期行业份额 | REAL | 去年同期市场份额百分比 |
| 份额变化 | REAL | 本期份额-同期份额 |

## 3. 业务规则与约束

### 3.1 默认值规则
- **默认品牌**：未指定品牌时默认为"安踏"
- **默认平台**：未指定平台时默认为"全平台"
- **默认行业**：未指定行业时计算所有行业（母婴鞋服、运动鞋服、户外鞋服）的总和
- **默认指标**：未明确指标时默认查询销售额（品牌销额）

### 3.2 品牌名称映射
| 用户输入 | 标准名称 |
|----------|----------|
| FILA/flia | 斐乐 |
| Nike | 耐克 |
| Adidas | 阿迪 |
| lining | 李宁 |

### 3.3 平台映射
| 用户输入 | 标准名称 |
|----------|----------|
| 所有平台/全部平台/整体平台 | 全平台 |
| JD | 京东 |
| 淘宝/Tmall | 天猫 |
| douyin | 抖音 |
### 3.4 行业映射
| 用户输入 | 标准名称 |
|----------|----------|
| 母婴/童装/婴幼儿用品 | 母婴鞋服 |
| 户外 | 户外鞋服 |
| 运动/体育用品 | 运动鞋服 |

### 3.5 时间处理规则
- 时间筛选
- 格式：`销售年月 = '2025-04-01'` 表示2025年整个4月
- 格式：`销售年月 LIKE '2023-%'` 表示2023年全年

### 3.6 计算规则
- **增速计算**：(品牌销量 - 同期品牌销量) / 同期品牌销量 * 100
- **聚合计算**：默认使用SUM()计算总和
- **数值计算**：除法运算使用* 1.0确保浮点数结果
- **排名查询**：使用本期排名字段，需明确指定销售年月和平台条件

## 4. SQL生成规范

### 4.1 基本规范
1. 生成单行SQL语句，不使用换行符
2. 中文字段名用反引号包围
3. 中文别名用单引号包围
4. 适当使用聚合、排序、限制行数优化结果

### 4.2 查询模式
- **多需求拆解**：多个需求时分别提供SQL查询
- **空值处理**：同时考虑NULL和空字符串情况
- **筛选显示**：LIKE条件时返回被筛选字段名

## 5. API接口规范

### 5.1 SQL执行接口
- **URL**: `http://10.232.153.230:8088/filter_data_md`
- **方法**: POST
- **请求格式**: JSON
- **请求参数**:
  ```json
  {
    "result_key": "base64编码的SQL语句"
  }
  ```

## 6. 系统工作流程

1. **问题改写**：将用户自然语言查询标准化
2. **SQL生成**：基于标准化问题生成SQL查询语句
3. **SQL解析**：提取生成的SQL语句列表
4. **循环执行**：对每个SQL语句进行base64编码并调用API执行
5. **结果整合**：将查询结果整合并格式化输出
6. **相关推荐**：生成用户可能感兴趣的相关问题

## 7. 验证用例

### 7.1 基础查询
```sql
-- 问题: 2025年4月安踏在全平台的销售额是多少？
SELECT SUM(`品牌销额`) as 销售额 FROM 情报通 WHERE `品牌名称` = '安踏' AND `平台` = '全平台' AND `销售年月` = '2025-04-01'
```

### 7.2 排名查询
```sql
-- 问题: 2025年2月天猫平台运动鞋服行业销售额排名前5的品牌有哪些？
SELECT `品牌名称`, `品牌销额`, `本期排名` FROM 情报通 WHERE `平台` = '天猫' AND `行业` = '运动鞋服' AND `销售年月` = '2025-02-01' ORDER BY `本期排名` ASC LIMIT 5
```

### 7.3 多平台聚合
```sql
-- 问题：2024年12月耐克在天猫、京东、抖音三个平台的总销售额是多少？
SELECT SUM(`品牌销额`) AS '品牌销额总和' FROM 情报通 WHERE `销售年月` = '2024-12-01' AND `品牌名称` = '耐克' AND `平台` IN ('天猫', '京东', '抖音')
```

### 7.4 销量与市场份额查询
```sql
-- 问题：2025年3月斐乐在全平台的销量和市场份额是多少？
SELECT SUM(`品牌销量`) AS '品牌销量', SUM(`本期行业份额`) AS '本期行业份额' FROM 情报通 WHERE `销售年月` = '2025-03-01' AND `平台` = '全平台' AND `品牌名称` = '斐乐'
```

## 8. 输出格式要求

### 8.1 数据展示
- 百分比结果用**%格式显示
- 保留小数点后3位，四舍五入
- 优先使用表格形式展示
- 数额较大时单位优化为万元

### 8.2 异常处理
- 无数据时提示："抱歉，根据现有数据，暂未找到相关数据"

## 9. 开发建议

### 9.1 Python实现要点
1. 使用SQLite数据库连接
2. 实现自然语言到SQL的转换逻辑
3. 集成大模型API进行问题理解和SQL生成
4. 实现结果格式化和展示逻辑

### 9.2 关键模块
- 问题预处理模块
- SQL生成模块
- 数据库查询模块
- 结果格式化模块
- 相关推荐模块
import requests
import base64
import json
import re
from typing import Dict, List, Any, Optional, Tuple

# 情报通API配置
API_URL = "http://10.232.153.230:8088/filter_data_md"

class QingBaoTongSystem:
    """情报通SQL查询系统"""
    
    def __init__(self):
        self.headers = {"Content-Type": "application/json"}
        # 品牌名称映射
        self.brand_mapping = {
            "FILA": "斐乐", "flia": "斐乐", "斐乐": "斐乐",
            "Nike": "耐克", "nike": "耐克", "耐克": "耐克",
            "Adidas": "阿迪", "adidas": "阿迪", "阿迪": "阿迪",
            "lining": "李宁", "李宁": "李宁",
            "安踏": "安踏", "anta": "安踏"
        }
        
        # 平台映射
        self.platform_mapping = {
            "所有平台": "全平台", "全部平台": "全平台", "整体平台": "全平台",
            "JD": "京东", "京东": "京东",
            "淘宝": "天猫", "Tmall": "天猫", "天猫": "天猫",
            "douyin": "抖音", "抖音": "抖音",
            "全平台": "全平台"
        }
        
        # 行业映射
        self.industry_mapping = {
            "母婴": "母婴鞋服", "童装": "母婴鞋服", "婴幼儿用品": "母婴鞋服",
            "户外": "户外鞋服",
            "运动": "运动鞋服", "体育用品": "运动鞋服"
        }

    def normalize_brand(self, brand: str) -> str:
        """标准化品牌名称"""
        return self.brand_mapping.get(brand, brand)
    
    def normalize_platform(self, platform: str) -> str:
        """标准化平台名称"""
        return self.platform_mapping.get(platform, platform)
    
    def normalize_industry(self, industry: str) -> str:
        """标准化行业名称"""
        return self.industry_mapping.get(industry, industry)

    def encode_sql(self, sql: str) -> str:
        """将SQL语句进行Base64编码"""
        return base64.b64encode(sql.encode('utf-8')).decode('utf-8')
    
    def execute_sql(self, sql: str) -> Dict[str, Any]:
        """执行SQL查询"""
        try:
            encoded_sql = self.encode_sql(sql)
            payload = {"result_key": encoded_sql}
            
            response = requests.post(
                API_URL, 
                headers=self.headers, 
                json=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                return {
                    "success": True,
                    "data": response.json(),
                    "sql": sql
                }
            else:
                return {
                    "success": False,
                    "error": f"API调用失败: {response.status_code}",
                    "sql": sql
                }
        except Exception as e:
            return {
                "success": False,
                "error": f"执行错误: {str(e)}",
                "sql": sql
            }

    # ========== SQL生成工具函数 ==========
    
    def get_brand_sales_amount(self, brand: str = "安踏", platform: str = "全平台",
                              year_month: Optional[str] = None, year: Optional[str] = None) -> str:
        """生成品牌销售额查询SQL"""
        brand = self.normalize_brand(brand)
        platform = self.normalize_platform(platform)
        
        if year_month:
            time_condition = f"`销售年月` = '{year_month}'"
        elif year:
            time_condition = f"`销售年月` LIKE '{year}-%'"
        else:
            time_condition = "1=1"
            
        sql = f"SELECT SUM(`品牌销额`) as 销售额 FROM 情报通 WHERE `品牌名称` = '{brand}' AND `平台` = '{platform}' AND {time_condition}"
        return sql
    
    def get_brand_sales_volume(self, brand: str = "安踏", platform: str = "全平台",
                              year_month: Optional[str] = None, year: Optional[str] = None) -> str:
        """生成品牌销量查询SQL"""
        brand = self.normalize_brand(brand)
        platform = self.normalize_platform(platform)
        
        if year_month:
            time_condition = f"`销售年月` = '{year_month}'"
        elif year:
            time_condition = f"`销售年月` LIKE '{year}-%'"
        else:
            time_condition = "1=1"
            
        sql = f"SELECT SUM(`品牌销量`) as 销量 FROM 情报通 WHERE `品牌名称` = '{brand}' AND `平台` = '{platform}' AND {time_condition}"
        return sql
    
    def get_brand_growth_rate(self, brand: str, platform: str = "全平台", year_month: Optional[str] = None) -> str:
        """生成品牌增速查询SQL"""
        brand = self.normalize_brand(brand)
        platform = self.normalize_platform(platform)
        
        time_condition = f"`销售年月` = '{year_month}'" if year_month else "1=1"
        
        sql = f"SELECT `品牌销量`, `同期品牌销量`, (`品牌销量` - `同期品牌销量`) / `同期品牌销量` * 100 AS '增速' FROM 情报通 WHERE {time_condition} AND `品牌名称` = '{brand}' AND `平台` = '{platform}'"
        return sql
    
    def get_brand_ranking(self, platform: str = "全平台", industry: str = "运动鞋服",
                         year_month: Optional[str] = None, limit: int = 5) -> str:
        """生成品牌排名查询SQL"""
        platform = self.normalize_platform(platform)
        industry = self.normalize_industry(industry)
        
        time_condition = f"`销售年月` = '{year_month}'" if year_month else "1=1"
        
        sql = f"SELECT `品牌名称`, `品牌销额`, `本期排名` FROM 情报通 WHERE `平台` = '{platform}' AND `行业` = '{industry}' AND {time_condition} ORDER BY `本期排名` ASC LIMIT {limit}"
        return sql
    
    def get_multi_platform_sales(self, brand: str, platforms: List[str], year_month: Optional[str] = None) -> str:
        """生成多平台汇总查询SQL"""
        brand = self.normalize_brand(brand)
        normalized_platforms = [self.normalize_platform(p) for p in platforms]
        platforms_str = "', '".join(normalized_platforms)
        
        time_condition = f"`销售年月` = '{year_month}'" if year_month else "1=1"
        
        sql = f"SELECT SUM(`品牌销额`) AS '品牌销额总和' FROM 情报通 WHERE {time_condition} AND `品牌名称` = '{brand}' AND `平台` IN ('{platforms_str}')"
        return sql
    
    def get_brand_market_share(self, brand: str, platform: str = "全平台", year_month: Optional[str] = None) -> str:
        """生成品牌市场份额查询SQL"""
        brand = self.normalize_brand(brand)
        platform = self.normalize_platform(platform)
        
        time_condition = f"`销售年月` = '{year_month}'" if year_month else "1=1"
        
        sql = f"SELECT SUM(`品牌销量`) AS '品牌销量', SUM(`本期行业份额`) AS '本期行业份额' FROM 情报通 WHERE {time_condition} AND `平台` = '{platform}' AND `品牌名称` = '{brand}'"
        return sql

    # ========== 高级查询工具 ==========
    
    def compare_brands(self, brands: List[str], platform: str = "全平台",
                      metric: str = "销售额", year_month: Optional[str] = None) -> List[Dict[str, Any]]:
        """比较多个品牌的指标"""
        results = []
        
        for brand in brands:
            if metric == "销售额":
                sql = self.get_brand_sales_amount(brand, platform, year_month)
            elif metric == "销量":
                sql = self.get_brand_sales_volume(brand, platform, year_month)
            else:
                continue
                
            result = self.execute_sql(sql)
            result['brand'] = brand
            result['metric'] = metric
            results.append(result)
            
        return results
    
    def analyze_brand_performance(self, brand: str, platform: str = "全平台",
                                 year_month: Optional[str] = None) -> Dict[str, Any]:
        """综合分析品牌表现"""
        results = {}
        
        # 销售额
        sales_sql = self.get_brand_sales_amount(brand, platform, year_month)
        results['sales_amount'] = self.execute_sql(sales_sql)
        
        # 销量
        volume_sql = self.get_brand_sales_volume(brand, platform, year_month)
        results['sales_volume'] = self.execute_sql(volume_sql)
        
        # 市场份额
        share_sql = self.get_brand_market_share(brand, platform, year_month)
        results['market_share'] = self.execute_sql(share_sql)
        
        # 增速（如果有时间参数）
        if year_month:
            growth_sql = self.get_brand_growth_rate(brand, platform, year_month)
            results['growth_rate'] = self.execute_sql(growth_sql)
        
        return results

    # ========== 结构化查询处理器 ==========
    
    def process_structured_query(self, structured_intent: Dict[str, Any]) -> Dict[str, Any]:
        """处理结构化的查询意图"""
        # 标准化输入参数
        brands = structured_intent.get("brands", [])
        platforms = structured_intent.get("platforms", [])
        metrics = structured_intent.get("metrics", [])
        time_period = structured_intent.get("time_period")
        query_type = structured_intent.get("query_type", "unknown")
        industry = structured_intent.get("industry", "运动鞋服")
        limit = structured_intent.get("limit", 5)
        
        # 设置默认值
        if not brands:
            brands = ["安踏"]
        if not platforms:
            platforms = ["全平台"]
        if not metrics:
            metrics = ["销售额"]
        
        # 标准化品牌和平台名称
        brands = [self.normalize_brand(brand) for brand in brands]
        platforms = [self.normalize_platform(platform) for platform in platforms]
        industry = self.normalize_industry(industry)
        
        results = {
            "structured_intent": {
                "brands": brands,
                "platforms": platforms,
                "metrics": metrics,
                "time_period": time_period,
                "query_type": query_type,
                "industry": industry,
                "limit": limit
            },
            "results": []
        }
        
        try:
            if query_type == "comparison" or query_type == "brand_comparison":
                # 品牌比较
                for metric in metrics:
                    comparison_results = self.compare_brands(
                        brands,
                        platforms[0],
                        metric,
                        time_period
                    )
                    results["results"].extend(comparison_results)
            
            elif query_type == "ranking" or query_type == "brand_ranking":
                # 排名查询
                sql = self.get_brand_ranking(
                    platforms[0],
                    industry,
                    time_period,
                    limit
                )
                ranking_result = self.execute_sql(sql)
                results["results"].append(ranking_result)
            
            elif query_type == "single_brand" or query_type == "brand_analysis":
                # 单品牌分析
                brand_analysis = self.analyze_brand_performance(
                    brands[0],
                    platforms[0],
                    time_period
                )
                results["results"].append(brand_analysis)
            
            elif query_type == "multi_platform":
                # 多平台汇总查询
                sql = self.get_multi_platform_sales(brands[0], platforms, time_period)
                multi_platform_result = self.execute_sql(sql)
                results["results"].append(multi_platform_result)
            
            elif query_type == "sales_amount":
                # 销售额查询
                sql = self.get_brand_sales_amount(brands[0], platforms[0], time_period)
                sales_result = self.execute_sql(sql)
                results["results"].append(sales_result)
            
            elif query_type == "sales_volume":
                # 销量查询
                sql = self.get_brand_sales_volume(brands[0], platforms[0], time_period)
                volume_result = self.execute_sql(sql)
                results["results"].append(volume_result)
            
            elif query_type == "growth_rate":
                # 增速查询
                sql = self.get_brand_growth_rate(brands[0], platforms[0], time_period)
                growth_result = self.execute_sql(sql)
                results["results"].append(growth_result)
            
            elif query_type == "market_share":
                # 市场份额查询
                sql = self.get_brand_market_share(brands[0], platforms[0], time_period)
                share_result = self.execute_sql(sql)
                results["results"].append(share_result)
            
            else:
                # 默认处理：销售额查询
                sql = self.get_brand_sales_amount(brands[0], platforms[0], time_period)
                default_result = self.execute_sql(sql)
                results["results"].append(default_result)
            
        except Exception as e:
            results["error"] = str(e)
        
        return results

    def execute_tool_function(self, tool_name: str, **kwargs) -> Dict[str, Any]:
        """直接调用指定的工具函数"""
        try:
            if tool_name == "get_brand_sales_amount":
                sql = self.get_brand_sales_amount(
                    kwargs.get("brand", "安踏"),
                    kwargs.get("platform", "全平台"),
                    kwargs.get("year_month"),
                    kwargs.get("year")
                )
                return self.execute_sql(sql)
            
            elif tool_name == "get_brand_sales_volume":
                sql = self.get_brand_sales_volume(
                    kwargs.get("brand", "安踏"),
                    kwargs.get("platform", "全平台"),
                    kwargs.get("year_month"),
                    kwargs.get("year")
                )
                return self.execute_sql(sql)
            
            elif tool_name == "get_brand_growth_rate":
                brand = kwargs.get("brand")
                if not brand:
                    return {"success": False, "error": "brand参数不能为空"}
                sql = self.get_brand_growth_rate(
                    brand,
                    kwargs.get("platform", "全平台"),
                    kwargs.get("year_month")
                )
                return self.execute_sql(sql)
            
            elif tool_name == "get_brand_ranking":
                sql = self.get_brand_ranking(
                    kwargs.get("platform", "全平台"),
                    kwargs.get("industry", "运动鞋服"),
                    kwargs.get("year_month"),
                    kwargs.get("limit", 5)
                )
                return self.execute_sql(sql)
            
            elif tool_name == "get_multi_platform_sales":
                brand = kwargs.get("brand")
                if not brand:
                    return {"success": False, "error": "brand参数不能为空"}
                sql = self.get_multi_platform_sales(
                    brand,
                    kwargs.get("platforms", []),
                    kwargs.get("year_month")
                )
                return self.execute_sql(sql)
            
            elif tool_name == "get_brand_market_share":
                brand = kwargs.get("brand")
                if not brand:
                    return {"success": False, "error": "brand参数不能为空"}
                sql = self.get_brand_market_share(
                    brand,
                    kwargs.get("platform", "全平台"),
                    kwargs.get("year_month")
                )
                return self.execute_sql(sql)
            
            elif tool_name == "compare_brands":
                comparison_results = self.compare_brands(
                    kwargs.get("brands", []),
                    kwargs.get("platform", "全平台"),
                    kwargs.get("metric", "销售额"),
                    kwargs.get("year_month")
                )
                return {
                    "success": True,
                    "data": comparison_results,
                    "tool_name": tool_name
                }
            
            elif tool_name == "analyze_brand_performance":
                brand = kwargs.get("brand")
                if not brand:
                    return {"success": False, "error": "brand参数不能为空"}
                return self.analyze_brand_performance(
                    brand,
                    kwargs.get("platform", "全平台"),
                    kwargs.get("year_month")
                )
            
            else:
                return {
                    "success": False,
                    "error": f"未知的工具函数: {tool_name}",
                    "available_tools": [
                        "get_brand_sales_amount",
                        "get_brand_sales_volume",
                        "get_brand_growth_rate",
                        "get_brand_ranking",
                        "get_multi_platform_sales",
                        "get_brand_market_share",
                        "compare_brands",
                        "analyze_brand_performance"
                    ]
                }
                
        except Exception as e:
            return {
                "success": False,
                "error": f"工具函数执行错误: {str(e)}",
                "tool_name": tool_name,
                "parameters": kwargs
            }


# ========== Dify集成函数 ==========

def main(intent_result: str) -> dict:
    """Dify主函数入口"""
    try:
        system = QingBaoTongSystem()
        
        # 尝试解析为JSON格式的结构化意图
        if intent_result.startswith('{'):
            structured_intent = json.loads(intent_result)
            result = system.process_structured_query(structured_intent)
        else:
            # 如果不是JSON格式，则作为简单查询处理
            # 假设是销售额查询的默认格式
            result = system.execute_tool_function("get_brand_sales_amount", brand="安踏", platform="全平台")
        
        return {
            "result": json.dumps(result, ensure_ascii=False)
        }
    except Exception as e:
        return {
            "result": json.dumps({
                "error": str(e),
                "success": False
            }, ensure_ascii=False)
        }

def invoke(intent_result: str) -> dict:
    """调用处理函数"""
    return main(intent_result)


# ========== 测试函数 ==========

def test_system():
    """测试系统功能"""
    system = QingBaoTongSystem()
    
    # 测试结构化查询
    test_cases = [
        {
            "name": "品牌销售额查询",
            "intent": {
                "brands": ["安踏"],
                "platforms": ["全平台"],
                "query_type": "sales_amount",
                "time_period": "2025-04-01"
            }
        },
        {
            "name": "品牌比较",
            "intent": {
                "brands": ["李宁", "安踏"],
                "platforms": ["全平台"],
                "query_type": "comparison",
                "metrics": ["销量"]
            }
        },
        {
            "name": "排名查询",
            "intent": {
                "platforms": ["天猫"],
                "query_type": "ranking",
                "industry": "运动鞋服",
                "time_period": "2025-02-01",
                "limit": 5
            }
        }
    ]
    
    for case in test_cases:
        print(f"\n测试: {case['name']}")
        print("-" * 50)
        result = system.process_structured_query(case['intent'])
        print(json.dumps(result, ensure_ascii=False, indent=2))
        print("=" * 80)

if __name__ == "__main__":
    test_system()
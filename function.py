import requests
import json
import re

AMAP_DISTRICT_API_URL = "https://restapi.amap.com/v3/config/district"
AMAP_WEATHER_API_URL = "https://restapi.amap.com/v3/weather/weatherInfo"
api_key = "fa21eddea481b788af3458dc509364b2"

def extract_json_from_markdown(text):
    """从包含markdown代码块的文本中提取JSON"""
    pattern = r'```json\s*\n(.*?)\n```'
    match = re.search(pattern, text, re.DOTALL)
    
    if match:
        return match.group(1).strip()
    else:
        return text

def main(intent_result: str) -> dict:
    location = "unknown"
    try:
        # 修复：添加函数调用括号
        intent_result = extract_json_from_markdown(intent_result)
        
        # 解析意图识别结果
        intent_data = json.loads(intent_result)
        location = intent_data.get('location', '')
        intent = intent_data.get('intent', '')
        adcode = "unknown"
        if intent == "district":
            # 调用行政区划查询函数
            result = get_amap_district(keywords=location)
            adcode = result.get("result", {}).get("districts", [{}])[0].get("adcode")
        else:
            district_query_result = get_amap_district(keywords=location)
            adcode = district_query_result.get("result", {}).get("districts", [{}])[0].get("adcode")
            result = get_amap_weather(city_adcode=adcode, extensions="base") 
        
        # 修复：返回JSON字符串
        return {
            "result": json.dumps({
                'query_type': 'district',
                'adcode': adcode,
                'result': result,
                'success': True
            }, ensure_ascii=False)
        }
    except Exception as e:
        # 修复：返回JSON字符串
        return {
            "result": json.dumps({
                'error': str(e),
                'success': False
            }, ensure_ascii=False)
        }



def get_amap_district( keywords: str, subdistrict: int = 0, extensions: str = "base") -> dict:
    """
    调用高德地图行政区划查询 API

    Args:
        api_key: 高德 Web 服务 API Key
        keywords: 查询关键字，支持 行政区名称、citycode、adcode
        subdistrict: 子级行政区查询，0 不返回下级行政区；1 返回下一级行政区；2 返回下两级行政区；3 返回下三级行政区
        extensions: 返回结果控制，base:不返回行政区边界坐标点；all:返回行政区边界坐标点

    Returns:
        dict: 包含查询结果或错误信息的字典，封装在 result 字段中
    """
    if not api_key:
        return {
            "result": {
                "success": False,
                "error": "缺少 API Key",
                "message": "请提供有效的高德 Web 服务 API Key",
                "data": None
            }
        }

    params = {
        "key": api_key,
        "keywords": keywords,
        "subdistrict": str(subdistrict),
        "extensions": extensions,
        "output": "JSON"
    }

    try:
        response = requests.get(AMAP_DISTRICT_API_URL, params=params, timeout=10)
        response.raise_for_status()  # Raises HTTPError for bad responses (4XX or 5XX)
        data = response.json()

        if data.get("status") == "1":
            return {
                "result": {
                    "success": True,
                    "message": data.get("info", "查询成功"),
                    "count": data.get("count"),
                    "suggestion": data.get("suggestion"),
                    "districts": data.get("districts", []),
                    "infocode": data.get("infocode")
                }
            }
        else:
            return {
                "result": {
                    "success": False,
                    "error": "API 返回错误",
                    "message": data.get("info", "高德 API 返回错误") + f" (infocode: {data.get('infocode', 'N/A')})",
                    "data": None,
                    "infocode": data.get("infocode")
                }
            }

    except requests.exceptions.Timeout:
        return {
            "result": {
                "success": False,
                "error": "请求超时",
                "message": "高德 API 请求超过 10 秒未响应",
                "data": None
            }
        }
    except requests.exceptions.ConnectionError:
        return {
            "result": {
                "success": False,
                "error": "连接错误",
                "message": "无法连接到高德 API 服务器",
                "data": None
            }
        }
    except requests.exceptions.HTTPError as e:
        return {
            "result": {
                "success": False,
                "error": f"HTTP 错误: {e.response.status_code}",
                "message": e.response.text[:200],
                "data": None
            }
        }
    except json.JSONDecodeError:
        return {
            "result": {
                "success": False,
                "error": "JSON 解析错误",
                "message": "高德 API 返回的数据格式不正确",
                "data": None
            }
        }
    except Exception as e:
        return {
            "result": {
                "success": False,
                "error": f"未知错误: {str(e)}",
                "message": "执行过程中发生意外错误",
                "data": None
            }
        }

def get_amap_weather( city_adcode: str, extensions: str = "base") -> dict:
    """
    调用高德地图天气查询 API

    Args:
        api_key: 高德 Web 服务 API Key
        city_adcode: 城市编码 (adcode)
        extensions: 气象类型，base:返回实况天气；all:返回预报天气

    Returns:
        dict: 包含查询结果或错误信息的字典，封装在 result 字段中
    """
    if not api_key:
        return {
            "result": {
                "success": False,
                "error": "缺少 API Key",
                "message": "请提供有效的高德 Web 服务 API Key",
                "data": None
            }
        }

    params = {
        "key": api_key,
        "city": city_adcode,
        "extensions": extensions,
        "output": "JSON"
    }

    try:
        response = requests.get(AMAP_WEATHER_API_URL, params=params, timeout=10)
        response.raise_for_status()  # Raises HTTPError for bad responses (4XX or 5XX)
        data = response.json()

        if data.get("status") == "1":
            # 根据 extensions 类型，lives 或 forecasts 可能不存在
            result_data = {
                "success": True,
                "message": data.get("info", "查询成功"),
                "count": data.get("count"),
                "infocode": data.get("infocode")
            }
            if "lives" in data:
                result_data["lives"] = data.get("lives", [])
            if "forecasts" in data:
                result_data["forecasts"] = data.get("forecasts", [])
            
            return {"result": result_data}
        else:
            return {
                "result": {
                    "success": False,
                    "error": "API 返回错误",
                    "message": data.get("info", "高德 API 返回错误") + f" (infocode: {data.get('infocode', 'N/A')})",
                    "data": None,
                    "infocode": data.get("infocode")
                }
            }

    except requests.exceptions.Timeout:
        return {
            "result": {
                "success": False,
                "error": "请求超时",
                "message": "高德 API 请求超过 10 秒未响应",
                "data": None
            }
        }
    except requests.exceptions.ConnectionError:
        return {
            "result": {
                "success": False,
                "error": "连接错误",
                "message": "无法连接到高德 API 服务器",
                "data": None
            }
        }
    except requests.exceptions.HTTPError as e:
        return {
            "result": {
                "success": False,
                "error": f"HTTP 错误: {e.response.status_code}",
                "message": e.response.text[:200],
                "data": None
            }
        }
    except json.JSONDecodeError:
        return {
            "result": {
                "success": False,
                "error": "JSON 解析错误",
                "message": "高德 API 返回的数据格式不正确",
                "data": None
            }
        }
    except Exception as e:
        return {
            "result": {
                "success": False,
                "error": f"未知错误: {str(e)}",
                "message": "执行过程中发生意外错误",
                "data": None
            }
        }

# 情报通SQL查询系统 - 最终实现方案

## 项目概述

基于`plain.md`要求实现的情报通SQL查询系统，专为dify python脚本环境设计，集成OpenRouter大模型进行结构化意图提取，实现了类似langraph的动态组合调用tool use效果。

## 🚀 核心特性

### ✅ 完全兼容Dify环境
- 只使用python 3 SDK内置库（除requests）
- 语法简洁，无奇淫技巧
- 严格按照dify模板返回string格式

### ✅ 集成OpenRouter大模型
- 使用OpenRouter API进行智能意图提取
- 支持结构化JSON格式输出
- 自动标准化品牌、平台、行业名称

### ✅ 类似LangGraph的动态工具组合
- 模块化工具设计，每个SQL查询封装为独立函数
- 支持动态参数传递和工具链式调用
- 可扩展的工具架构

### ✅ 丰富的查询功能
- 8种核心SQL生成工具
- 支持多种查询模式
- 灵活的参数配置

## 📁 文件结构

```
├── qingbaotong_system.py       # 核心系统实现
├── openrouter_integration.py   # OpenRouter集成演示
├── run_tests.py               # 快速测试脚本
├── test_qingbaotong_pytest.py # 完整pytest测试
├── README_final.md            # 最终说明文档
├── dev.md                     # 开发文档（原始）
├── test_sql.py               # 测试用例（原始）
└── plain.md                  # 需求文档（原始）
```

## 🛠️ 核心组件

### 1. QingBaoTongSystem 主类

```python
class QingBaoTongSystem:
    """情报通SQL查询系统核心类"""
    
    # 标准化方法
    def normalize_brand(self, brand: str) -> str
    def normalize_platform(self, platform: str) -> str  
    def normalize_industry(self, industry: str) -> str
    
    # SQL执行
    def execute_sql(self, sql: str) -> Dict[str, Any]
    
    # SQL生成工具函数（8个核心工具）
    def get_brand_sales_amount(self, ...)     # 品牌销售额
    def get_brand_sales_volume(self, ...)     # 品牌销量
    def get_brand_growth_rate(self, ...)      # 品牌增速
    def get_brand_ranking(self, ...)          # 品牌排名
    def get_multi_platform_sales(self, ...)  # 多平台汇总
    def get_brand_market_share(self, ...)     # 品牌市场份额
    def compare_brands(self, ...)             # 品牌比较
    def analyze_brand_performance(self, ...)  # 品牌综合分析
    
    # 新增：结构化查询处理
    def process_structured_query(self, structured_intent: Dict) -> Dict
    def execute_tool_function(self, tool_name: str, **kwargs) -> Dict
```

### 2. OpenRouter集成

```python
class OpenRouterIntegration:
    """OpenRouter与情报通系统集成"""
    
    def extract_intent_with_openrouter(self, user_query: str) -> dict
    def process_query_with_llm(self, user_query: str) -> dict
```

### 3. Dify集成函数

```python
def main(intent_result: str) -> dict:
    """Dify主函数入口，支持JSON格式结构化输入"""
    
def invoke(intent_result: str) -> dict:
    """调用处理函数"""
```

## 🔧 支持的查询类型

| 查询类型 | query_type | 功能描述 | 示例 |
|---------|------------|----------|------|
| 销售额查询 | `sales_amount` | 查询品牌销售额 | `2024年{品牌}在全平台的销售额` |
| 销量查询 | `sales_volume` | 查询品牌销量 | `2024年{品牌}在全平台的销量` |
| 增速查询 | `growth_rate` | 计算销量增速 | `{品牌}在{平台}的销量增速` |
| 排名查询 | `ranking` | 品牌排名查询 | `{平台}{行业}销售额排名前5` |
| 品牌比较 | `comparison` | 多品牌对比 | `比较{品牌1}和{品牌2}的销量` |
| 多平台汇总 | `multi_platform` | 跨平台数据汇总 | `{品牌}在多个平台的总销售额` |
| 市场份额 | `market_share` | 市场份额查询 | `{品牌}的销量和市场份额` |
| 综合分析 | `brand_analysis` | 品牌全面分析 | `{品牌}的综合表现分析` |

## 📝 使用示例

### 1. 结构化查询（推荐方式）

```python
from qingbaotong_system import QingBaoTongSystem

system = QingBaoTongSystem()

# 结构化意图
structured_intent = {
    "brands": ["安踏"],
    "platforms": ["全平台"],
    "query_type": "sales_amount",
    "time_period": "2025-04-01"
}

result = system.process_structured_query(structured_intent)
```

### 2. 直接工具调用

```python
# 直接调用工具函数
result = system.execute_tool_function(
    "get_brand_sales_amount",
    brand="安踏",
    platform="全平台", 
    year_month="2025-04-01"
)
```

### 3. Dify集成

```python
# Dify函数调用（JSON格式输入）
intent_json = json.dumps({
    "brands": ["安踏"],
    "platforms": ["全平台"],
    "query_type": "sales_amount",
    "time_period": "2025-04-01"
}, ensure_ascii=False)

result = main(intent_json)
print(result["result"])  # 返回JSON字符串
```

### 4. OpenRouter智能查询

```python
from openrouter_integration import OpenRouterIntegration

integration = OpenRouterIntegration()

# 自然语言查询
result = integration.process_query_with_llm(
    "2025年4月安踏在全平台的销售额是多少？"
)
```

## 🤖 OpenRouter集成流程

```mermaid
graph TD
    A[用户自然语言查询] --> B[OpenRouter API]
    B --> C[结构化意图提取]
    C --> D[QingBaoTongSystem]
    D --> E[SQL生成]
    E --> F[API执行]
    F --> G[结果返回]
```

### OpenRouter提示词模板

系统使用专门设计的提示词进行意图提取：

```json
{
  "brands": ["品牌1", "品牌2"],
  "platforms": ["平台1"],
  "metrics": ["指标1"],
  "time_period": "时间",
  "query_type": "查询类型",
  "industry": "行业",
  "limit": 数字
}
```

## 🔄 动态工具组合示例

### 品牌比较分析

```python
# 比较李宁和安踏的综合表现
brands = ["李宁", "安踏"]
comparison_results = {}

for brand in brands:
    # Tool 1: 销售额
    sales_result = system.execute_tool_function(
        "get_brand_sales_amount", 
        brand=brand, platform="全平台", year_month="2025-03-01"
    )
    
    # Tool 2: 销量
    volume_result = system.execute_tool_function(
        "get_brand_sales_volume",
        brand=brand, platform="全平台", year_month="2025-03-01"
    )
    
    # Tool 3: 市场份额
    share_result = system.execute_tool_function(
        "get_brand_market_share",
        brand=brand, platform="全平台", year_month="2025-03-01"
    )
    
    # 组合结果
    comparison_results[brand] = {
        "销售额": sales_result,
        "销量": volume_result,
        "市场份额": share_result
    }
```

## 🧪 测试验证

### 运行快速测试
```bash
python run_tests.py
```

### 运行完整pytest测试
```bash
python -m pytest test_qingbaotong_pytest.py -v
```

### 运行OpenRouter集成演示
```bash
python openrouter_integration.py
```

## 📊 测试结果

所有测试用例100%通过：

- ✅ 基础功能测试
- ✅ 原始测试用例验证
- ✅ 结构化查询功能
- ✅ Dify集成功能  
- ✅ 工具组合功能
- ✅ 系统扩展性测试

## 🎯 实现亮点

### 1. 完全满足plain.md要求

- ✅ **基于dev.md**：严格按照数据表结构和业务规则
- ✅ **基于test_sql.py**：所有测试用例转换为function工具
- ✅ **动态SQL生成**：入参动态化，SQL动态生成
- ✅ **去掉意图解析**：按要求移除，使用前置大模型处理
- ✅ **类似LangGraph**：实现动态组合并调用tool use
- ✅ **方便扩展**：模块化设计，易于添加新工具
- ✅ **Dify兼容**：完全符合dify python脚本要求

### 2. OpenRouter集成优势

- 🤖 智能意图理解：自然语言 → 结构化意图
- 🎯 精确参数提取：品牌、平台、时间自动标准化
- 🔄 完整处理流程：意图提取 → SQL生成 → 执行查询
- 📱 无缝dify集成：支持JSON格式输入输出

### 3. 技术架构优势

- 🛠️ 模块化设计：每个功能独立封装，易于测试和维护
- 🔧 灵活工具系统：支持直接调用和动态组合
- 📈 高度可扩展：新增工具只需添加函数和注册
- 🔒 类型安全：完整的类型注解和参数验证

## 🚀 生产环境部署

### Dify配置

```python
# dify代码执行节点
def main(intent_result: str) -> dict:
    """
    入参：intent_result - JSON格式的结构化意图
    返回：{"result": "JSON字符串"}
    """
    # 系统会自动处理
```

### 前置节点配置

在dify中配置OpenRouter调用节点：

1. **意图提取节点**：调用OpenRouter API
2. **SQL生成节点**：调用本系统main函数
3. **结果处理节点**：格式化输出

## 📈 扩展指南

### 添加新查询工具

```python
def get_new_analysis(self, brand: str, **kwargs) -> str:
    """新的分析工具"""
    # 1. 参数标准化
    brand = self.normalize_brand(brand)
    
    # 2. SQL生成
    sql = f"SELECT ... FROM 情报通 WHERE `品牌名称` = '{brand}'"
    
    return sql
```

### 注册到工具系统

```python
def execute_tool_function(self, tool_name: str, **kwargs) -> Dict:
    # 添加新工具分支
    elif tool_name == "get_new_analysis":
        sql = self.get_new_analysis(**kwargs)
        return self.execute_sql(sql)
```

## 🎉 总结

本实现方案完美满足了plain.md的所有要求，提供了：

1. **生产级别的代码质量**：完整的错误处理、类型注解、测试覆盖
2. **灵活的架构设计**：支持多种调用方式和扩展需求
3. **智能的LLM集成**：OpenRouter + 结构化意图处理
4. **完善的工具生态**：8个核心工具 + 动态组合能力
5. **无缝的dify集成**：即插即用，支持JSON格式交互

系统已准备好投入生产环境使用！🚀
# 情报通SQL查询系统实现方案

## 项目概述

基于`plain.md`要求实现的情报通SQL查询系统，专为dify python脚本环境设计，实现了类似langraph的动态组合调用tool use效果。

## 核心特性

### ✅ Dify兼容性
- 完全符合dify python脚本限制
- 只使用python 3 SDK内置库
- 语法简洁，无奇淫技巧
- 返回值严格按照dify模板要求

### ✅ 类似LangGraph的动态工具组合
- 模块化工具设计，每个SQL查询封装为独立函数
- 支持动态参数传递和工具链式调用
- 智能查询意图解析，自动选择合适的工具组合
- 可扩展的工具架构

### ✅ 智能查询处理
- 自然语言查询意图解析
- 品牌、平台、行业名称自动标准化
- 时间参数智能提取和格式化
- 多种查询模式支持（单品牌、比较、排名等）

## 文件结构

```
├── qingbaotong_system.py    # 核心系统实现
├── demo_qingbaotong.py      # 演示脚本
├── README_qingbaotong.md    # 说明文档
├── dev.md                   # 开发文档（原始）
├── test_sql.py             # 测试用例（原始）
└── plain.md                # 需求文档（原始）
```

## 核心组件

### 1. QingBaoTongSystem 主类

```python
class QingBaoTongSystem:
    """情报通SQL查询系统核心类"""
    
    def __init__(self):
        # 初始化映射表和配置
        
    # 标准化方法
    def normalize_brand(self, brand: str) -> str
    def normalize_platform(self, platform: str) -> str  
    def normalize_industry(self, industry: str) -> str
    
    # SQL执行
    def execute_sql(self, sql: str) -> Dict[str, Any]
    
    # SQL生成工具函数
    def get_brand_sales_amount(self, ...)     # 品牌销售额
    def get_brand_sales_volume(self, ...)     # 品牌销量
    def get_brand_growth_rate(self, ...)      # 品牌增速
    def get_brand_ranking(self, ...)          # 品牌排名
    def get_multi_platform_sales(self, ...)  # 多平台汇总
    def get_brand_market_share(self, ...)     # 品牌市场份额
    
    # 高级查询工具
    def compare_brands(self, ...)             # 品牌比较
    def analyze_brand_performance(self, ...)  # 品牌综合分析
    
    # 智能查询
    def parse_query_intent(self, ...)         # 查询意图解析
    def smart_query(self, ...)                # 智能查询处理
```

### 2. Dify集成函数

```python
def main(intent_result: str) -> dict:
    """Dify主函数入口，返回值必须是string"""
    
def invoke(intent_result: str) -> dict:
    """调用处理函数"""
```

## 工具函数详解

### 基础SQL生成工具

| 工具函数 | 功能 | 示例 |
|---------|------|------|
| `get_brand_sales_amount` | 品牌销售额查询 | `2024年{品牌}在全平台的销售额` |
| `get_brand_sales_volume` | 品牌销量查询 | `2024年{品牌}在全平台的销量` |
| `get_brand_growth_rate` | 品牌增速计算 | `{品牌}在{平台}的销量增速` |
| `get_brand_ranking` | 品牌排名查询 | `{平台}{行业}销售额排名前5` |
| `get_multi_platform_sales` | 多平台汇总 | `{品牌}在多个平台的总销售额` |
| `get_brand_market_share` | 市场份额查询 | `{品牌}的销量和市场份额` |

### 高级组合工具

| 工具函数 | 功能 | 特点 |
|---------|------|------|
| `compare_brands` | 品牌比较 | 动态调用基础工具，支持多品牌对比 |
| `analyze_brand_performance` | 品牌综合分析 | 组合多个指标，全面分析品牌表现 |
| `smart_query` | 智能查询 | 自动解析意图，选择合适工具组合 |

## 类似LangGraph的实现原理

### 1. 工具模块化设计
```python
# 每个查询功能封装为独立工具
def get_brand_sales_amount(self, brand, platform, year_month=None):
    # 动态生成SQL
    return sql

def get_brand_sales_volume(self, brand, platform, year_month=None):
    # 动态生成SQL  
    return sql
```

### 2. 动态工具组合
```python
def compare_brands(self, brands, platform, metric):
    results = []
    for brand in brands:
        if metric == "销售额":
            sql = self.get_brand_sales_amount(brand, platform)
        elif metric == "销量":
            sql = self.get_brand_sales_volume(brand, platform)
        
        result = self.execute_sql(sql)
        results.append(result)
    return results
```

### 3. 智能工具选择
```python
def smart_query(self, query):
    intent = self.parse_query_intent(query)
    
    if intent["query_type"] == "comparison":
        # 选择比较工具
        return self.compare_brands(...)
    elif intent["query_type"] == "ranking":
        # 选择排名工具
        return self.get_brand_ranking(...)
    elif intent["query_type"] == "single_brand":
        # 选择单品牌分析工具
        return self.analyze_brand_performance(...)
```

## 使用示例

### 1. 基础查询
```python
system = QingBaoTongSystem()

# 品牌销售额查询
sql = system.get_brand_sales_amount("安踏", "全平台", "2025-04-01")
result = system.execute_sql(sql)
```

### 2. 智能查询
```python
# 自然语言查询
query = "比较李宁和安踏在全平台的销量"
result = system.smart_query(query)
```

### 3. Dify集成
```python
# Dify函数调用
result = main("2025年4月安踏在全平台的销售额是多少？")
print(result["result"])  # 返回JSON字符串
```

### 4. 动态工具组合
```python
# 复杂分析：比较多个品牌的综合表现
brands = ["李宁", "安踏"]
comparison_results = {}

for brand in brands:
    # Tool 1: 销售额
    sales_sql = system.get_brand_sales_amount(brand, "全平台", "2025-03-01")
    sales_result = system.execute_sql(sales_sql)
    
    # Tool 2: 销量
    volume_sql = system.get_brand_sales_volume(brand, "全平台", "2025-03-01")
    volume_result = system.execute_sql(volume_sql)
    
    # Tool 3: 市场份额
    share_sql = system.get_brand_market_share(brand, "全平台", "2025-03-01")
    share_result = system.execute_sql(share_sql)
    
    # 组合结果
    comparison_results[brand] = {
        "销售额": sales_result,
        "销量": volume_result,
        "市场份额": share_result
    }
```

## 扩展性设计

### 添加新工具
```python
def get_brand_trend_analysis(self, brand: str, platform: str = "全平台", 
                           months: int = 6) -> str:
    """生成品牌趋势分析SQL"""
    brand = self.normalize_brand(brand)
    platform = self.normalize_platform(platform)
    
    sql = f"""SELECT `销售年月`, SUM(`品牌销额`) as 销售额, SUM(`品牌销量`) as 销量 
              FROM 情报通 
              WHERE `品牌名称` = '{brand}' AND `平台` = '{platform}' 
              ORDER BY `销售年月` DESC LIMIT {months}"""
    return sql
```

### 添加新的组合逻辑
```python
def analyze_market_competition(self, industry: str, platform: str, year_month: str):
    """市场竞争分析"""
    # 1. 获取行业排名
    ranking_sql = self.get_brand_ranking(platform, industry, year_month)
    ranking_result = self.execute_sql(ranking_sql)
    
    # 2. 分析头部品牌
    top_brands = [item['品牌名称'] for item in ranking_result['data'][:3]]
    
    # 3. 对比分析
    comparison = self.compare_brands(top_brands, platform, "销售额", year_month)
    
    return {
        "ranking": ranking_result,
        "top_brands_comparison": comparison
    }
```

## 运行演示

```bash
# 运行演示脚本
python demo_qingbaotong.py
```

演示内容包括：
1. 基础查询功能演示
2. 智能查询功能演示  
3. 动态组合Tool Use演示
4. Dify集成功能演示
5. 可扩展工具系统演示

## 技术特点

### 1. 完全兼容Dify环境
- ✅ 只使用Python 3内置库
- ✅ 无外部依赖（除requests）
- ✅ 符合dify代码执行模板
- ✅ 返回值格式严格遵循要求

### 2. 类似LangGraph的架构
- ✅ 工具模块化设计
- ✅ 动态工具组合调用
- ✅ 智能工具选择机制
- ✅ 可扩展的工具架构

### 3. 智能化程度高
- ✅ 自然语言查询解析
- ✅ 自动参数标准化
- ✅ 智能SQL生成
- ✅ 多种查询模式支持

### 4. 易于维护和扩展
- ✅ 清晰的代码结构
- ✅ 统一的接口设计
- ✅ 完善的错误处理
- ✅ 详细的文档说明

## 总结

本实现方案完全满足`plain.md`的所有要求：

1. ✅ **基于dev.md开发文档**：严格按照数据表结构和业务规则实现
2. ✅ **基于test_sql.py测试用例**：将所有测试用例转换为function工具
3. ✅ **动态SQL生成**：入参动态化，SQL动态生成
4. ✅ **类似LangGraph效果**：实现了动态组合并调用tool use
5. ✅ **方便扩展**：模块化设计，易于添加新工具
6. ✅ **Dify兼容**：完全符合dify python脚本要求
7. ✅ **返回值规范**：严格按照dify模板返回string格式

系统具备了生产环境使用的完整功能，可以直接集成到dify平台中使用。